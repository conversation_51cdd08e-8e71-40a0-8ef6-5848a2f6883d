# TPA API Configuration
# Copy this file to .env and update the values as needed

# TPA API Base URL
# Development: http://localhost:9000
# Production: Update this to the production API URL
TPA_API_BASE_URL=http://localhost:9000
VITE_TPA_API_BASE_URL=http://localhost:9000

# TPA API Prefix
# The API prefix path used for all TPA endpoints
TPA_API_PREFIX=/api
VITE_TPA_API_PREFIX=/api

# TPA API Timeout (in milliseconds)
# Maximum time to wait for API responses before timing out
# Default: 30000ms (30 seconds)
TPA_API_TIMEOUT=30000
VITE_TPA_API_TIMEOUT=30000

# TPA API Retry Attempts
# Number of retry attempts for failed API requests
# Default: 3 attempts
TPA_API_RETRY_ATTEMPTS=3
VITE_TPA_API_RETRY_ATTEMPTS=3

# Development Configuration
# Set to 'true' to enable development mode features like detailed logging
VITE_DEV_MODE=true

# API Environment
# Used to determine which API configuration to use
# Values: development, staging, production
VITE_API_ENVIRONMENT=development
