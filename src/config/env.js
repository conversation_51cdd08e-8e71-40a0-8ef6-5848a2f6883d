/**
 * Environment Configuration
 * 
 * This module provides access to environment variables for the TPA API integration.
 * It uses Vite's built-in environment variable handling.
 * 
 * Note: In Vite, only variables prefixed with VITE_ are exposed to the client.
 * Server-side variables (like TPA_API_*) need to be handled differently.
 */

// Client-side environment variables (prefixed with VITE_)
export const clientConfig = {
  devMode: import.meta.env.VITE_DEV_MODE === 'true',
  apiEnvironment: import.meta.env.VITE_API_ENVIRONMENT || 'development',
  mode: import.meta.env.MODE,
  isDev: import.meta.env.DEV,
  isProd: import.meta.env.PROD
};

// TPA API Configuration
// These will be loaded from environment variables during build time
// For now, we'll use default values that match our .env file
export const tpaApiConfig = {
  baseUrl: import.meta.env.VITE_TPA_API_BASE_URL || 'http://localhost:9000',
  prefix: import.meta.env.VITE_TPA_API_PREFIX || '/api',
  timeout: parseInt(import.meta.env.VITE_TPA_API_TIMEOUT || '30000'),
  retryAttempts: parseInt(import.meta.env.VITE_TPA_API_RETRY_ATTEMPTS || '3')
};

// Full API base URL
export const getApiBaseUrl = () => {
  return `${tpaApiConfig.baseUrl}${tpaApiConfig.prefix}`;
};

// Environment validation
export const validateEnvironment = () => {
  const errors = [];
  
  if (!tpaApiConfig.baseUrl) {
    errors.push('TPA_API_BASE_URL is required');
  }
  
  if (!tpaApiConfig.prefix) {
    errors.push('TPA_API_PREFIX is required');
  }
  
  if (isNaN(tpaApiConfig.timeout) || tpaApiConfig.timeout <= 0) {
    errors.push('TPA_API_TIMEOUT must be a positive number');
  }
  
  if (isNaN(tpaApiConfig.retryAttempts) || tpaApiConfig.retryAttempts < 0) {
    errors.push('TPA_API_RETRY_ATTEMPTS must be a non-negative number');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Log configuration in development mode
if (clientConfig.devMode && clientConfig.isDev) {
  console.log('Environment Configuration:', {
    client: clientConfig,
    tpaApi: tpaApiConfig,
    fullApiUrl: getApiBaseUrl()
  });
  
  const validation = validateEnvironment();
  if (!validation.isValid) {
    console.warn('Environment validation errors:', validation.errors);
  }
}
